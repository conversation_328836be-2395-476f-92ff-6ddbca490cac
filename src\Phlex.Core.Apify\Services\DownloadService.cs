using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Constants;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using System.Text.Json;
using System.Web;

namespace Phlex.Core.Apify.Services;

public class DownloadService(ILogger<DownloadService> logger) : IDownloadService
{
    public async Task BinaryDownloadAsync(HttpClient client,
        string requestTemplateUrl,
        string[] keys,
        IDownloadStorage storage,
        IReadOnlyDictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            foreach (var key in keys.Where(k => !DownloadConstants.ExcludeFiles.Any(k.ToUpper().Contains)).ToArray())
            {
                var requestUrl = requestTemplateUrl.Replace("##key##", key);
                using (var stream = await client.GetStreamAsync(requestUrl, cancellationToken))
                {
                    await storage.WriteDataItemAsync(HttpUtility.UrlEncode(key), stream, metadata, cancellationToken);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: An error occurred: {ErrorMessage}", ex.Message);
        }
    }

    public async Task DownloadItemsAsync(string dataSetId,
        List<object> items,
        IDownloadStorage storage,
        IReadOnlyDictionary<string, string>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        var count = 1;
        foreach (var item in items.Where(i => i != null).ToArray())
        {
            var json = item.ToString();
            try
            {
                if (json != null)
                {
                    // Extract URLs and metadata from the dataset item and combine with webhook metadata
                    var combinedMetadata = await ExtractAndCombineMetadataAsync(json, metadata, cancellationToken);

                    using (var stream = GenerateStreamFromString(json))
                    {
                        await storage.WriteDataItemAsync(dataSetId + "_" + count + ".json", stream, combinedMetadata, cancellationToken);
                    }
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Apify: An error occurred downloading item: {Json}", json);
            }
            count++;
        }
    }

    public async Task<List<WebScraperDatasetItem>> GetWebScraperDatasetsAsync(string dataSetId, List<object> items, CancellationToken cancellationToken = default)
    {
        var webScraperDataSetItems = new List<WebScraperDatasetItem>();
        foreach (var item in items.Where(i => i != null).ToArray())
        {
            var json = item.ToString();
            try
            {
                if (json != null)
                {
                    using var stream = GenerateStreamFromString(json);
                    JsonSerializerOptions jsonSerializerOptions = new()
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    var datasetItem = await JsonSerializer.DeserializeAsync<WebScraperDatasetItem>(
                        stream,
                        jsonSerializerOptions,
                        cancellationToken
                    );

                    if (datasetItem != null)
                        webScraperDataSetItems.Add(datasetItem);
                    else
                        throw new InvalidDataException("Apify: Failed to deserialize JSON into WebScraperDatasetItem.");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Apify: An error occurred parsing item: {Json}", json);
            }
        }

        return webScraperDataSetItems;
    }

    private async Task<IReadOnlyDictionary<string, string>?> ExtractAndCombineMetadataAsync(
        string json,
        IReadOnlyDictionary<string, string>? webhookMetadata,
        CancellationToken cancellationToken)
    {
        try
        {
            using var stream = GenerateStreamFromString(json);
            JsonSerializerOptions jsonSerializerOptions = new()
            {
                PropertyNameCaseInsensitive = true
            };

            var datasetItem = await JsonSerializer.DeserializeAsync<WebScraperDatasetItem>(
                stream,
                jsonSerializerOptions,
                cancellationToken
            );

            if (datasetItem == null)
            {
                return webhookMetadata;
            }

            // Create a new dictionary combining webhook metadata with extracted URLs
            var combinedMetadata = new Dictionary<string, string>();

            // Add webhook metadata first (preserve existing metadata)
            if (webhookMetadata != null)
            {
                foreach (var kvp in webhookMetadata)
                {
                    combinedMetadata[kvp.Key] = kvp.Value;
                }
            }

            // Extract URLs from the dataset item - these are essential for URL-to-Journal connection
            if (!string.IsNullOrEmpty(datasetItem.url))
            {
                combinedMetadata["url"] = datasetItem.url;
            }

            // Extract crawl depth as it's mentioned in the requirements
            if (datasetItem.crawl != null)
            {
                combinedMetadata["crawlDepth"] = datasetItem.crawl.depth.ToString();
            }

            return combinedMetadata.Count > 0 ? combinedMetadata : webhookMetadata;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Apify: Failed to extract metadata from dataset item, using webhook metadata only");
            return webhookMetadata;
        }
    }



    private static MemoryStream GenerateStreamFromString(string s)
    {
        var stream = new MemoryStream();
        var writer = new StreamWriter(stream);
        writer.Write(s);
        writer.Flush();
        stream.Position = 0;
        return stream;
    }
}
