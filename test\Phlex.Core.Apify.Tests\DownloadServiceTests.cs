using System.Text.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using Phlex.Core.Apify.Services;
using Microsoft.Extensions.Logging;
using Xunit;

namespace Phlex.Core.Apify.Tests;

public class DownloadServiceTests
{
    private readonly ILogger<DownloadService> _logger;
    private readonly IDownloadStorage _storageSubstitute;
    private readonly DownloadService _downloadService;
    private const string TestDataSetId = "testDataSet";

    public DownloadServiceTests()
    {
        _logger = Substitute.For<ILogger<DownloadService>>();
        _storageSubstitute = Substitute.For<IDownloadStorage>();
        _downloadService = new DownloadService(_logger);
    }

    [Fact]
    public async Task BinaryDownloadAsync_ValidKey_WritesToStorage()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        var requestTemplateUrl = "http://example.com/##key##";
        string[] keys = ["validKey"];
        _storageSubstitute.WriteDataItemAsync(Arg.Any<string>(),
                Arg.Any<Stream>(),
                Arg.Any<IReadOnlyDictionary<string, string>?>(),
                Arg.Any<CancellationToken>())
            .Returns(Task.CompletedTask);

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storageSubstitute);

        // Assert
        await _storageSubstitute.Received(1)
            .WriteDataItemAsync(Arg.Any<string>(),
                Arg.Any<Stream>(),
                Arg.Any<IReadOnlyDictionary<string, string>?>(),
                Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task BinaryDownloadAsync_ExcludedKey_DoesNotWriteToStorage()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler());
        var requestTemplateUrl = "http://example.com/##key##";
        string[] keys = ["CRAWLEE_STATE"];

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storageSubstitute);

        // Assert
        await _storageSubstitute.DidNotReceive()
            .WriteDataItemAsync(Arg.Any<string>(),
                Arg.Any<Stream>(),
                Arg.Any<IReadOnlyDictionary<string, string>?>(),
                Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task DownloadItemsAsync_ValidItems_WritesToStorage()
    {
        // Arrange
        var items = new List<object> { new { Name = "Item1" }, new { Name = "Item2" } };
        _storageSubstitute.WriteDataItemAsync(Arg.Any<string>(), Arg.Any<Stream>(), Arg.Any<IReadOnlyDictionary<string, string>?>(), Arg.Any<CancellationToken>())
                           .Returns(Task.CompletedTask);

        // Act
        await _downloadService.DownloadItemsAsync(TestDataSetId, items, _storageSubstitute);

        // Assert
        await _storageSubstitute.Received(2)
            .WriteDataItemAsync(Arg.Any<string>(),
                Arg.Any<Stream>(),
                Arg.Any<IReadOnlyDictionary<string, string>?>(),
                Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task DownloadItemsAsync_NullItems_DoesNotWriteToStorage()
    {
        // Arrange
        var items = new List<object>();

        // Act
        await _downloadService.DownloadItemsAsync(TestDataSetId, items, _storageSubstitute);

        // Assert
        await _storageSubstitute.DidNotReceive()
            .WriteDataItemAsync(Arg.Any<string>(),
                Arg.Any<Stream>(),
                Arg.Any<IReadOnlyDictionary<string, string>?>(),
                Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_ValidItems_ReturnsDatasetItems()
    {
        // Arrange
        var items = new List<object>
            {
                JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.com", text = "Item1" }),
                JsonSerializer.Serialize(new WebScraperDatasetItem { url = "http://example.org", text = "Item2" })
            };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(TestDataSetId, items, CancellationToken.None);

        // Assert
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task GetWebScraperDatasetsAsync_InvalidJson_LogsError()
    {
        // Arrange
        var items = new List<object> { "Invalid JSON" };

        // Act
        var result = await _downloadService.GetWebScraperDatasetsAsync(TestDataSetId, items, CancellationToken.None);

        // Assert
        Assert.Empty(result);

        _logger.Received().Log(
             LogLevel.Error,
             Arg.Any<EventId>(),
             Arg.Is<object>(v => v.ToString()!.Contains("An error occurred parsing item")),
             Arg.Any<Exception>(),
             Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task BinaryDownloadAsync_Exception_LogsError()
    {
        // Arrange
        var client = new HttpClient(new MockHttpMessageHandler { ThrowException = true });
        string requestTemplateUrl = "http://example.com/##key##";
        string[] keys = { "validKey" };

        // Act
        await _downloadService.BinaryDownloadAsync(client, requestTemplateUrl, keys, _storageSubstitute);

        // Assert
        _logger.Received().Log(
             LogLevel.Error,
             Arg.Any<EventId>(),
             Arg.Is<object>(v => v.ToString()!.Contains("An error occurred")),
             Arg.Any<Exception>(),
             Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public async Task DownloadItemsAsync_Exception_LogsError()
    {
        // Arrange
        var items = new List<object> { new { Name = "Item1" } };
        _storageSubstitute.WriteDataItemAsync(Arg.Any<string>(),
                Arg.Any<Stream>(),
                Arg.Any<IReadOnlyDictionary<string, string>?>(),
                Arg.Any<CancellationToken>())
            .Throws(new Exception("Test exception"));

        // Act
        await _downloadService.DownloadItemsAsync(TestDataSetId, items, _storageSubstitute);

        // Assert
        _logger.Received().Log(
         LogLevel.Error,
         Arg.Any<EventId>(),
         Arg.Is<object>(v => v.ToString()!.Contains("An error occurred downloading item")),
         Arg.Any<Exception>(),
         Arg.Any<Func<object, Exception?, string>>());
    }


}

public class MockHttpMessageHandler : HttpMessageHandler
{
    public bool ThrowException { get; set; }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        if (ThrowException)
        {
            throw new HttpRequestException("Test exception");
        }

        var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
        {
            Content = new StreamContent(new MemoryStream("test data"u8.ToArray()))
        };
        return await Task.FromResult(response);
    }
}
