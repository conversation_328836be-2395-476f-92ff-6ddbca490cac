﻿using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.Interfaces;

public interface IDownloadService
{
    Task DownloadItemsAsync(string dataSetId, List<object> items, IDownloadStorage storage,
        IReadOnlyDictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);

    Task BinaryDownloadAsync(HttpClient client, string requestTemplateUrl, string[] keys, IDownloadStorage storage,
        IReadOnlyDictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);

    Task<List<WebScraperDatasetItem>> GetWebScraperDatasetsAsync(string dataSetId, List<object> items, CancellationToken cancellationToken = default);
}
