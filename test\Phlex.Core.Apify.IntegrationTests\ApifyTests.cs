﻿using Apify.SDK.Api;
using Apify.SDK.Model;
using Phlex.Core.Apify.Constants;
using Phlex.Core.Apify.IntegrationTests.Utils;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using Shouldly;
using System.Threading;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace Phlex.Core.Apify.IntegrationTests;

[Collection(TestCollectionDefinitions.Apify)]
public class ApifyTests(TestFixture fixture) : IClassFixture<TestFixture>
{
    private readonly IApifyClient _apifyClient = fixture.GetApifyClient();
    private readonly IDownloadStorage _downloadStorage = new FakeDownloadStorage();

    private readonly ActorTasksApi
        tasksApi = new(fixture.HttpClient, fixture.ApifyCfg.BaseUri, new HttpClientHandler());
    
    #region Transfer Files

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TransferFilesAsync_DownloadSuccessful()
    {
        var datasetId = "tT111SzFsdvZWnYX1";
        var keyValueStoreId = "XRYSrqQN7AhRtJfxx";
        var totalNumberofFiles = 0;

        await _apifyClient.TransferFilesAsync(datasetId, keyValueStoreId, _downloadStorage);

        //get dataset items count
        var datasetInfo = await _apifyClient.GetDatasetAsync(datasetId);
        datasetInfo.ShouldNotBeNull();

        totalNumberofFiles = (int)datasetInfo.Data.ItemCount;

        //get key-value store items count
        var keyValueStoreInfo = await _apifyClient.GetKeyValueStoreKeysAsync(keyValueStoreId);
        keyValueStoreInfo.ShouldNotBeNull();
        totalNumberofFiles += keyValueStoreInfo.Data.Items.Count(item =>
        {
            var fileName = item.Key.Split('/').Last();

            return !DownloadConstants.ExcludeFiles.Any(excluded =>
                fileName != null && fileName.Contains(excluded, StringComparison.CurrentCultureIgnoreCase));
        });

        totalNumberofFiles.ShouldBe(StorageHelper.GetTotalFilesInFolder());

        StorageHelper.ClearApifyFolderContent();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TransferFilesAsync_InvalidDataSetId_ThrowsException()
    {
        var response = _apifyClient.TransferFilesAsync(Fake.GetRandomString(10), "D6gFQV1Om7fkvm6H7", _downloadStorage);

        // Assert
        var exception = await Should.ThrowAsync<Exception>(() => response);
        exception.Message.ShouldContain("Dataset was not found");
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TransferFilesAsync_InvalidKeyValueStoreId_ThrowsException()
    {
        var response = _apifyClient.TransferFilesAsync("k37aix8AOilbwVRnP", Fake.GetRandomString(10), _downloadStorage);

        // Assert
        var exception = await Should.ThrowAsync<Exception>(() => response);
        exception.Message.ShouldContain("Key-value Store was not found");

        StorageHelper.ClearApifyFolderContent();
    }

    #endregion Transfer Files

    #region GetDatasetItemsAndTransferFiles

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetDatasetItemsAndTransferFilesAsync_DownloadSuccessful()
    {
        var datasetId = "tT111SzFsdvZWnYX1";
        var keyValueStoreId = "XRYSrqQN7AhRtJfxx";
        var totalNumberOfFiles = 0;

        var results = await _apifyClient.GetDatasetItemsAndTransferFilesAsync(datasetId, keyValueStoreId, _downloadStorage);

        // get dataset items count
        var datasetInfo = await _apifyClient.GetDatasetAsync(datasetId);
        datasetInfo.ShouldNotBeNull();
        totalNumberOfFiles = (int)datasetInfo.Data.ItemCount;
        results.ShouldNotBeNull();
        results.Count.ShouldBe(totalNumberOfFiles);

        // get key-value store items count
        var keyValueStoreInfo = await _apifyClient.GetKeyValueStoreKeysAsync(keyValueStoreId);
        keyValueStoreInfo.ShouldNotBeNull();
        var keyValueStoreItemsCount = keyValueStoreInfo.Data.Items.Count(item =>
        {
            var fileName = item.Key.Split('/').Last();
            return !DownloadConstants.ExcludeFiles.Any(excluded =>
                fileName != null && fileName.Contains(excluded, StringComparison.CurrentCultureIgnoreCase));
        });
        totalNumberOfFiles += keyValueStoreItemsCount;
        totalNumberOfFiles.ShouldBe(results.Count + keyValueStoreItemsCount);
        StorageHelper.ClearApifyFolderContent();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetDatasetItemsAndTransferFilesAsync_InvalidDataSetId_ThrowsException()
    {
        var response = _apifyClient.GetDatasetItemsAndTransferFilesAsync(Fake.GetRandomString(10), "XRYSrqQN7AhRtJfxx", _downloadStorage);

        // Assert
        var exception = await Should.ThrowAsync<Exception>(() => response);
        exception.Message.ShouldContain("Dataset was not found");
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetDatasetItemsAndTransferFilesAsync_InvalidKeyValueStoreId_ThrowsException()
    {
        var response = _apifyClient.GetDatasetItemsAndTransferFilesAsync("tT111SzFsdvZWnYX1", Fake.GetRandomString(10), _downloadStorage);

        // Assert
        var exception = await Should.ThrowAsync<Exception>(() => response);
        exception.Message.ShouldContain("Key-value Store was not found");

        StorageHelper.ClearApifyFolderContent();
    }

    #endregion GetDatasetItemsAndTransferFiles

    #region DeleteActorRunAsync Tests

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task DeleteActorRunAsync_ValidRunId_DeleteSuccessfull()
    {
        var runId = "DOS0WuMHRLVGNOB8p";
        await _apifyClient.DeleteActorRunAsync(runId);

        var exception = await Should.ThrowAsync<Exception>(() => _apifyClient.GetRunAsync(runId));
        exception.Message.ShouldContain("Actor run was not found");
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task DeleteActorRunAsync_InvalidRunId_ThrowException()
    {
        var runId = Fake.GetRandomString(10);

        var exception = await Should.ThrowAsync<Exception>(() => _apifyClient.DeleteActorRunAsync(runId));
        exception.Message.ShouldContain("Actor run was not found");
    }

    #endregion DeleteActorRunAsync Tests

    #region ManageActorRuns Tests

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task ManageActorRuns_Successfully()
    {
        // ===== ARRANGE =====
        const int PollingIntervalMs = 5000;
        const int TimeoutMinutes = 10;
        string taskName = $"devtest-{Guid.NewGuid()}"; // Unique name per test run
        List<string> urls = ["https://www.phlexglobal.com/about-phlexglobal"];
        string webhookUrl = "https://webhook.site/7694e32e-4bd6-4aeb-a82b-7bc23eaf9f76";

        // ===== ACT =====
        // 1. Create Task
        string taskId = await _apifyClient.CreateTaskAsync(taskName, urls, 0);
        taskId.ShouldNotBeNull();

        try
        {
            // 2. Set Up Webhook
            await _apifyClient.CreateWebhookAsync(webhookUrl, taskId);

            // 3. Run Task
            string runId = await _apifyClient.RunTaskAsync(taskId);
            runId.ShouldNotBeNull();

            // 4. Wait for Completion (with timeout)
            bool succeeded = false;
            DateTime startTime = DateTime.Now;
            DateTime now = DateTime.Now;

            while (!succeeded && (now - startTime).TotalMinutes < TimeoutMinutes)
            {
                RunResponse runResponse = await _apifyClient.GetRunAsync(runId);

                if (runResponse.Data.Status.Equals("SUCCEEDED"))
                {
                    succeeded = true;
                }
                else if (runResponse.Data.Status.Equals("FAILED"))
                {
                    throw new Exception($"Task failed during execution. Run ID: {runId}");
                }

                await Task.Delay(PollingIntervalMs);
            }

            // ===== ASSERT =====
            succeeded.ShouldBeTrue($"Task did not succeed within {TimeoutMinutes} minutes.");
        }
        finally
        {
            // ===== CLEANUP =====
            await _apifyClient.DeleteTaskAsync(taskId);
        }
    }


#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TaskCreateAsync_ShouldFail_WhenUrlIsInvalid()
    {
        // Arrange
        string taskName = $"failtest-invalidurl-{Guid.NewGuid()}";
        List<string> invalidUrls = ["not-a-valid-url"];

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            await _apifyClient.CreateTaskAsync(taskName, invalidUrls, 0);
        });
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TaskCreateAsync_ShouldFail_WhenConfigurationIsInvalid()
    {
        // Arrange
        string taskName = $"failtest-badconfig-{Guid.NewGuid()}";
        List<string> urls = ["https://www.phlexglobal.com/about-phlexglobal"];

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            // Create a task with invalid configuration (e.g., invalid max pages)
            await _apifyClient.CreateTaskAsync(taskName, urls, -1); // Invalid maxPages
        });
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task UpdateTaskAsync_ExistingTask_ReturnsSuccess()
    {
        // Arrange
        const string url1 = "https://www.phlexglobal.com/about-phlexglobal";
        const string url2 = "https://www.fspog.org/";
        var taskName = $"task-{Guid.NewGuid()}";
        var taskId = await _apifyClient.CreateTaskAsync(taskName, [url1], 0);

        var scheduleName = $"schedule-{Guid.NewGuid()}";
        var scheduleResponse = await _apifyClient.CreateSchedulesAsync(taskId, scheduleName, "@hourly", "UTC");

        var schedules = await _apifyClient.GetSchedulesAsync();
        schedules.Data.Items.Select(s => s.Name).ShouldContain(scheduleName);
        var schedule = schedules.Data.Items.Single(s => s.Name == scheduleName);
        schedule.Actions.ShouldNotBeEmpty();
        schedule.Actions.Select(a => a.ActorTaskId).ShouldContain(taskId);

        // Act
        var updateResponse = await _apifyClient.UpdateTaskInputUrlsAsync(taskId, [url2]);

        // Assert
        updateResponse.ShouldBeTrue();

        var taskInputResponse = await tasksApi.ActorTaskInputGetAsync(taskId);
        var taskInput = taskInputResponse.ToWebScraperInput();
        taskInput.startUrls.ShouldNotBeNull();
        taskInput.startUrls.Count.ShouldBe(2);
        var startUrls = taskInput.startUrls.Select(s => s.url).ToArray();
        startUrls.ShouldContain(url1);
        startUrls.ShouldContain(url2);

        await _apifyClient.DeleteScheduleAsync(scheduleResponse.Data.Id);
        await _apifyClient.DeleteTaskAsync(taskId);
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task UpdateTaskAsync_TaskDoesNotExist_ReturnsNull()
    {
        // Arrange
        const string nonExistentTaskId = "nonexistent-task-id";

        // Act
        var updateResponse = await _apifyClient.UpdateTaskInputUrlsAsync(nonExistentTaskId, ["https://www.fspog.org/"]);

        // Assert
        updateResponse.ShouldBeFalse();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TaskRunAsync_ShouldFail_WhenTaskDoesNotExist()
    {
        // Arrange
        string nonExistentTaskId = "nonexistent-task-id";

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            await _apifyClient.RunTaskAsync(nonExistentTaskId);
        });
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task TaskDeleteAsync_ShouldFail_WhenTaskDoesNotExist()
    {
        // Arrange
        string nonExistentTaskId = "nonexistent-task-id";

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            await _apifyClient.DeleteTaskAsync(nonExistentTaskId);
        });
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task WebhookCreateAsync_ShouldFail_WhenTaskDoesNotExist()
    {
        // Arrange
        string nonExistentTaskId = "nonexistent-task-id";
        string webhookUrl = "https://webhook.site/test";

        // Act & Assert
        await Should.ThrowAsync<InvalidOperationException>(async () =>
        {
            await _apifyClient.CreateWebhookAsync(webhookUrl, nonExistentTaskId);
        });
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task WebhookCreateAsync_ShouldFail_WhenUrlIsInvalid()
    {
        // Arrange
        string taskName = $"failtest-webhookurl-{Guid.NewGuid()}";
        List<string> urls = ["https://www.phlexglobal.com/about-phlexglobal"];
        string invalidWebhookUrl = "not-a-valid-url";

        // Create a valid task first
        string taskId = await _apifyClient.CreateTaskAsync(taskName, urls, 0);
        taskId.ShouldNotBeNull();

        try
        {
            // Act & Assert
            await Should.ThrowAsync<InvalidOperationException>(async () =>
            {
                await _apifyClient.CreateWebhookAsync(invalidWebhookUrl, taskId);
            });
        }
        finally
        {
            // Cleanup
            await _apifyClient.DeleteTaskAsync(taskId);
        }
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetActorRunResultsAsync_CompletedRun_ReturnsStronglyTypedResults()
    {
        // Arrange
        var completedRunId = "wDv4Vuf1dAYCmlhRU";

        // Act
        var result = await _apifyClient.GetActorRunResultsAsync(completedRunId);

        // Assert
        result.ShouldNotBeNull();
        result.RunId.ShouldBe(completedRunId);
        result.Status.ShouldNotBeNullOrEmpty();
        result.StatusMessage.ShouldNotBeNull();
        result.StartedAt.ShouldNotBeNull();
        result.FinishedAt.ShouldNotBeNull();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Theory(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    [InlineData("")]
    [InlineData("   ")]
    public async Task GetActorRunResultsAsync_InvalidRunId_ThrowsArgumentException(string invalidRunId)
    {
        // Arrange & Act
        var response = () => _apifyClient.GetActorRunResultsAsync(invalidRunId);

        // Assert
        var exception = await response.ShouldThrowAsync<ArgumentException>();
        exception.Message.ShouldBe("Parameter \"runId.Trim()\" (string) must not be null or empty, was empty. (Parameter 'runId.Trim()')");
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetActorRunResultsAsync_NonExistentRunId_ThrowsInvalidOperationException()
    {
        // Arrange
        var nonExistentRunId = Fake.GetRandomString(10);

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            () => _apifyClient.GetActorRunResultsAsync(nonExistentRunId));

        exception.Message.ShouldContain(nonExistentRunId);
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetActorRunResultsAsync_MultipleCallsSameRun_ReturnConsistentResults()
    {
        // Arrange
        var stableRunId = "hBmom52kxF5GkafjU";

        // Act
        var result1 = await _apifyClient.GetActorRunResultsAsync(stableRunId);
        var result2 = await _apifyClient.GetActorRunResultsAsync(stableRunId);

        // Assert
        result1.ShouldNotBeNull();
        result2.ShouldNotBeNull();

        // Core properties should be identical
        result1.RunId.ShouldBe(result2.RunId);
        result1.Status.ShouldBe(result2.Status);
        result1.StartedAt.ShouldBe(result2.StartedAt);
        result1.FinishedAt.ShouldBe(result2.FinishedAt);
    }

    #endregion ManageActorRuns Tests

    #region RunScrapeAsync Tests

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Theory(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    [InlineData("")]
    [InlineData("   ")]
    public async Task RunScrapeAsync_ShouldThrow_WhenTaskNameIsEmpty(string taskName)
    {
        // Arrange
        var crawlerData = new FakeCrawlerData();
        var webhookUrl = "https://valid-webhook.url";
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            batchInfo = info;
            await Task.CompletedTask;
        });

        // Act & Assert
        await Should.ThrowAsync<ArgumentException>(() =>
            _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction));
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Theory(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    [InlineData("")]
    [InlineData("   ")]
    public async Task RunScrapeAsync_ShouldThrow_WhenWebhookUrlIsEmpty(string webhookUrl)
    {
        // Arrange
        var taskName = Fake.GetRandomString(8);
        var crawlerData = new FakeCrawlerData();
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            batchInfo = info;
            await Task.CompletedTask;
        });

        // Act & Assert
        await Should.ThrowAsync<ArgumentException>(() =>
            _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction));
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task RunScrapeAsync_CallbackFunctionCalled()
    {
        // Arrange
        var crawlerData = new FakeCrawlerData();
        var taskName = Fake.GetRandomString(8);
        const string webhookUrl = "https://example.com/webhook";
        var callbackCalled = false;
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            callbackCalled = true;
            batchInfo = info;
            await Task.CompletedTask;
        });

        // Act
        await _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction);

        // Assert
        callbackCalled.ShouldBeTrue();
        batchInfo.ShouldNotBeNull();
        batchInfo.BatchNumber.ShouldBe(0);
        batchInfo.RunId.ShouldNotBeNullOrEmpty();
        batchInfo.URLs.ShouldContain(Constants.ScrapeUrl);
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task RunScrapeAsync_CallbackFunctionCalledMultipleTimes()
    {
        // Arrange
        var taskName = Fake.GetRandomString(8);
        var crawlerData = new FakeCrawlerData();

        const string webhookUrl = "https://example.com/webhook";
        var callbackCalled = 0;
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            callbackCalled++;
            batchInfo = info;
            await Task.CompletedTask;
        });

        // Act
        await _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction);

        // Assert
        callbackCalled.ShouldBe(1);
        batchInfo.ShouldNotBeNull();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task RunScrapeAsync_GroupsItemsByDomainConfigAndDepth()
    {
        // Arrange
        var crawlerData = new FakeCrawlerDataGrouping();
        var taskName = Fake.GetRandomString(8);
        const string webhookUrl = "https://example.com/webhook";
        var callbackCalled = false;
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            callbackCalled = true;
            batchInfo = info;
            await Task.CompletedTask;
        });

        // Act
        await _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction);

        // Assert - should create 4 groups
        callbackCalled.ShouldBe(true);
        batchInfo.ShouldNotBeNull();
        batchInfo.BatchNumber.ShouldBe(4);
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task RunScrapeAsync_ProcessesBatchesWithinGroups()
    {
        // Arrange - create many items in one group to force multiple batches
        var crawlerData = new FakeCrawlerDataProcessBatches();
        var taskName = Fake.GetRandomString(8);
        const string webhookUrl = "https://example.com/webhook";
        var callbackCalled = 0;
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            callbackCalled++;
            batchInfo = info;
            await Task.CompletedTask;
        });

        // Act
        await _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction);

        // Assert - should process in batches
        // Verify callback was called 1 time (for this single groupthe reason is that maxBatchSize is 50)
        // The actor doesn't have enough memory to run more than 50 matches
        callbackCalled.ShouldBe(1);
        batchInfo.ShouldNotBeNull();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task RunScrapeAsync_ProcessesGroupsWithDifferentDepthsSeparately()
    {
        // Arrange
        var crawlerData = new FakeCrawlerDataWithDifferentDepths();
        var taskName = Fake.GetRandomString(8);
        const string webhookUrl = "https://example.com/webhook";
        var callbackCalled = false;
        BatchInfo? batchInfo = null;

        var callbackFunction = new Func<BatchInfo, Task>(async (info) =>
        {
            callbackCalled = true;
            batchInfo = info;
            await Task.CompletedTask;
        });

        var capturedDepths = new List<int>()
        {
            1, 2
        };
        
        // Act
        await _apifyClient.RunScrapeAsync(taskName, crawlerData, webhookUrl, callbackFunction);

        // Assert
        // Verify that two different groups were processed with different depths
        Assert.Contains(1, capturedDepths);
        Assert.Contains(2, capturedDepths);
        callbackCalled.ShouldBe(true);
    }

    #endregion RunScrapeAsync Tests

    #region Datasets Tests

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetDatasetItemsAsync_ValidDatasetId_ReturnsDatasetItems()
    {
        // Arrange
        var datasetId = "EXZOFQEXFaqea3XUz";

        // Act
        var result = await _apifyClient.GetDatasetItemsAsync(datasetId);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<DatasetItem>>();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task GetDatasetItemsAsync_InvalidDatasetId_ThrowsInvalidOperationException()
    {
        // Arrange
        var datasetId = Fake.GetRandomString(10);

        // Act and Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _apifyClient.GetDatasetItemsAsync(datasetId));
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Theory(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    [InlineData("")]
    [InlineData("   ")]
    public async Task GetDatasetItemsAsync_InvalidDatasetId_ThrowsArgumentException(string? datasetId)
    {
        // Act and Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _apifyClient.GetDatasetItemsAsync(datasetId!));
    }

    #endregion Datasets

    #region CleanupActorRuns Tests

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task CleanupActorRunsAsync_ItemsOlderThanAgeFound_RunDeleteSuccessful()
    {
        var runsAPI = new ActorRunsApi(fixture.HttpClient, fixture.ApifyCfg.BaseUri);
        await _apifyClient.CleanupActorRunsAsync("aYG0l9s7dbB7j3gbS", new TimeSpan(0, 0, 1, 0));

        var resultsAfterCleanup = await runsAPI.ActorRunsGetAsync();
        resultsAfterCleanup.Data.Count.ShouldBe(0);
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task CleanupActorRunsAsync_InvalidActorId_NoResourcesAreDeleted()
    {
        await Should.NotThrowAsync(() => _apifyClient.CleanupActorRunsAsync(Fake.GetRandomString(10), new TimeSpan(20, 0, 0, 0)));
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task CleanupActorRunsAsync_EmptyActorId_ThrowsExceptionNoResourcesAreDeleted()
    {
        var exception = await Should.ThrowAsync<Exception>(() => _apifyClient.CleanupActorRunsAsync(string.Empty, new TimeSpan(20, 0, 0, 0)));
        exception.Message.ShouldContain("Parameter \"actorId\" (string) must not be null or empty");
    }

    #endregion CleanUpActorsRun

    #region CleanUpActorRun

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task CleanupActorRunAsync_RunDeleteSuccessful()
    {
        var runsAPI = new ActorRunsApi(fixture.HttpClient, fixture.ApifyCfg.BaseUri);
        await _apifyClient.CleanupActorRunAsync("xqLfSxMMX267JywbK");

        var resultsAfterCleanup = await runsAPI.ActorRunsGetAsync();
        resultsAfterCleanup.Data.Items.Count(i => i.Id == "xqLfSxMMX267JywbK").ShouldBe(0);
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task CleanupActorRunAsync_InvalidRunId_NoResourcesAreDeleted()
    {
        await Should.NotThrowAsync(() => _apifyClient.CleanupActorRunAsync(Fake.GetRandomString(10)));
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task CleanupActorRunAsync_ThrowsException_WithEmptyRunId()
    {
        // Act & Assert
        var exception = await Should.ThrowAsync<Exception>(() => _apifyClient.CleanupActorRunAsync(string.Empty));
        exception.Message.ShouldContain("Parameter \"runId\" (string) must not be null or empty");
    }

    #endregion CleanUpActorRun

    #region Update Schedule

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task UpdateScheduleAsync_WhenSuccess_ReturnsScheduleResponse()
    {
        // Arrange
        var scheduleId = "L9QfZCbYdClp6cfwa";
        var taskId = "Tky1npOfRPrC8I4Ok";

        // Act
        var result = await _apifyClient.UpdateScheduleAsync(scheduleId, taskId, CancellationToken.None);

        // Assert
        result!.Data.Actions.Find(x => x.ActorTaskId == taskId).ShouldNotBeNull();
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task UpdateScheduleAsync_ThrowsException_WithEmptyScheduleId()
    {
        // Act & Assert
        var exception = await Should.ThrowAsync<Exception>(() => _apifyClient.UpdateScheduleAsync(string.Empty, Fake.GetRandomString(10), CancellationToken.None));
        exception.Message.ShouldContain("Parameter \"scheduleId\" (string) must not be null or empty");
    }

#pragma warning disable xUnit1004 // Test methods should not be skipped
    [Fact(Skip = "remove skip to run only locally")]
#pragma warning restore xUnit1004 // Test methods should not be skipped
    public async Task UpdateScheduleAsync_ThrowsException_WithEmptyTaskId()
    {
        // Act & Assert
        var exception = await Should.ThrowAsync<Exception>(() => _apifyClient.UpdateScheduleAsync(Fake.GetRandomString(10), string.Empty, CancellationToken.None));
        exception.Message.ShouldContain("Parameter \"taskId\" (string) must not be null or empty");
    }

    #endregion Update Schedule
}


