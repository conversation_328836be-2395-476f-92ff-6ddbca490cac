using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;

namespace Phlex.Core.Apify.Webhook.Controllers;

[ApiController]
[AllowAnonymous]
[IgnoreAntiforgeryToken]
[Route("api/webhook/apify")]
public class ApifyWebhookController : ControllerBase
{
    private readonly IApifyNotification client;
    private readonly ApifyWebhookSettings webhookSettings;

    private readonly ILogger<ApifyWebhookController> logger;

    public ApifyWebhookController(IApifyNotification client,
        IOptions<ApifyWebhookSettings> apifyWebhookSettings,
        ILogger<ApifyWebhookController> logger
        )
    {
        this.client = client;
        this.webhookSettings = apifyWebhookSettings.Value;
        this.logger = logger;
    }

    [HttpPost]
    public IActionResult Receive([FromBody] ApifyWebhookPayload payload)
    {
        if (payload is null)
        {
            logger.LogError("Apify: Received null payload");
            return BadRequest("Invalid payload: payload is null");
        }

        string? token = Request.Headers.Authorization;

        if (token is not null && token.Equals("Bearer " + webhookSettings.SecretToken))
        {
            if (!Constants.ApifyEventTypes.IsValidEventType(payload.eventType))
            {
                logger.LogError("Apify: Invalid event type received: {EventType}", payload.eventType ?? "null");
                return BadRequest($"Invalid event type: {payload.eventType ?? "null"}");
            }

            if (!IsValidPayload(payload))
            {
                logger.LogError("Apify: Invalid payload structure received");
                return BadRequest("Invalid payload: missing required fields");
            }

            switch (payload.eventType)
            {
                case Constants.ApifyEventTypes.ActorRunSucceeded:
                    client.RunSucceeded(payload);
                    break;
                case Constants.ApifyEventTypes.ActorRunAborted:
                case Constants.ApifyEventTypes.ActorRunTimedOut:
                    client.RunFailed(payload);
                    break;
                default:
                    logger.LogWarning("Apify: Unexpected event type after validation: {EventType}", payload.eventType);
                    client.RunFailed(payload);
                    break;
            }

            return Ok();
        }
        else
        {
            string? ip = this.HttpContext.GetServerVariable("REMOTE_HOST");
            if (ip is null)
            {
                ip = this.HttpContext.GetServerVariable("REMOTE_ADDR");
                ip ??= "null";
            }
            logger.LogWarning("Apify: Call to webhook receiver enpoint without valid security token. Remote IP: {IP}", ip);
            return Unauthorized();
        }
    }

    private static bool IsValidPayload(ApifyWebhookPayload payload)
    {
        if (payload.eventData is null || payload.resource is null)
        {
            return false;
        }

        if (string.IsNullOrEmpty(payload.eventData.actorId) &&
            string.IsNullOrEmpty(payload.eventData.actorTaskId) &&
            string.IsNullOrEmpty(payload.eventData.actorRunId))
        {
            return false;
        }

        return true;
    }
}
